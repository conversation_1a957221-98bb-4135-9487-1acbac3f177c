## Application Details
|               |
| ------------- |
|**Generation Date and Time**<br>Sun Sep 28 2025 18:54:17 GMT+0530 (India Standard Time)|
|**App Generator**<br>SAP Fiori Application Generator|
|**App Generator Version**<br>1.18.7|
|**Generation Platform**<br>Visual Studio Code|
|**Template Used**<br>List Report Page V2|
|**Service Type**<br>SAP System (ABAP On-Premise)|
|**Service URL**<br>http://s4ha.unvired.io:8001/sap/opu/odata/sap/API_EQUIPMENT|
|**Module Name**<br>equip-safety-test-app|
|**Application Title**<br>Equipment Safety Testing|
|**Namespace**<br>com.unvired.demo.equipment|
|**UI5 Theme**<br>sap_horizon|
|**UI5 Version**<br>1.114.0|
|**Enable Code Assist Libraries**<br>False|
|**Enable TypeScript**<br>True|
|**Add Eslint configuration**<br>False|
|**Main Entity**<br>Equipment|
|**Navigation Entity**<br>to_Classification|

## equip-safety-test-app

An SAP Fiori Equipment Safety Testing App

### Starting the generated app

-   This app has been generated using the SAP Fiori tools - App Generator, as part of the SAP Fiori tools suite.  To launch the generated application, run the following from the generated application root folder:

```
    npm start
```

- It is also possible to run the application using mock data that reflects the OData Service URL supplied during application generation.  In order to run the application with Mock Data, run the following from the generated app root folder:

```
    npm run start-mock
```

#### Pre-requisites:

1. Active NodeJS LTS (Long Term Support) version and associated supported NPM version.  (See https://nodejs.org)


