<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx" Version="4.0">
    <edmx:Reference Uri="https://sap.github.io/odata-vocabularies/vocabularies/Common.xml">
        <edmx:Include Namespace="com.sap.vocabularies.Common.v1" Alias="Common"/>
    </edmx:Reference>
    <edmx:Reference Uri="https://sap.github.io/odata-vocabularies/vocabularies/UI.xml">
        <edmx:Include Namespace="com.sap.vocabularies.UI.v1" Alias="UI"/>
    </edmx:Reference>
    <edmx:Reference Uri="https://sap.github.io/odata-vocabularies/vocabularies/Communication.xml">
        <edmx:Include Namespace="com.sap.vocabularies.Communication.v1" Alias="Communication"/>
    </edmx:Reference>
    <edmx:Reference Uri="/sap/opu/odata/sap/API_EQUIPMENT/$metadata">
        <edmx:Include Namespace="cds_api_equipment"/>
    </edmx:Reference>
    <edmx:DataServices>
        <Schema xmlns="http://docs.oasis-open.org/odata/ns/edm" Namespace="local">
            <Annotations Target="cds_api_equipment.EquipmentType">
                <Annotation Term="UI.LineItem">
                    <Collection>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="Equipment"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="EquipmentName"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="EquipmentCategory"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="TechnicalObjectType"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="MaintenancePlant"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="AssetLocation"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="WorkCenter"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="FunctionalLocationLabelName"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="AssetManufacturerName"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="ManufacturerPartTypeName"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="ManufacturerSerialNumber"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="OperationStartDate"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="ValidityStartDate"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="ValidityEndDate"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="CreationDate"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="CreatedByUser"/>
                        </Record>
                    </Collection>
                </Annotation>

                <!-- Header Information for Object Page -->
                <Annotation Term="UI.HeaderInfo">
                    <Record Type="UI.HeaderInfoType">
                        <PropertyValue Property="TypeName" String="Equipment"/>
                        <PropertyValue Property="TypeNamePlural" String="Equipment"/>
                        <PropertyValue Property="Title">
                            <Record Type="UI.DataField">
                                <PropertyValue Property="Value" Path="Equipment"/>
                            </Record>
                        </PropertyValue>
                        <PropertyValue Property="Description">
                            <Record Type="UI.DataField">
                                <PropertyValue Property="Value" Path="EquipmentName"/>
                            </Record>
                        </PropertyValue>
                    </Record>
                </Annotation>

                <!-- Object Page Facets -->
                <Annotation Term="UI.Facets">
                    <Collection>
                        <Record Type="UI.ReferenceFacet">
                            <PropertyValue Property="ID" String="GeneralInformation"/>
                            <PropertyValue Property="Label" String="General Information"/>
                            <PropertyValue Property="Target" AnnotationPath="@UI.FieldGroup#GeneralInformation"/>
                        </Record>
                        <Record Type="UI.ReferenceFacet">
                            <PropertyValue Property="ID" String="TechnicalData"/>
                            <PropertyValue Property="Label" String="Technical Data"/>
                            <PropertyValue Property="Target" AnnotationPath="@UI.FieldGroup#TechnicalData"/>
                        </Record>
                        <Record Type="UI.ReferenceFacet">
                            <PropertyValue Property="ID" String="LocationData"/>
                            <PropertyValue Property="Label" String="Location & Organization"/>
                            <PropertyValue Property="Target" AnnotationPath="@UI.FieldGroup#LocationData"/>
                        </Record>
                        <Record Type="UI.ReferenceFacet">
                            <PropertyValue Property="ID" String="ManufacturerData"/>
                            <PropertyValue Property="Label" String="Manufacturer Data"/>
                            <PropertyValue Property="Target" AnnotationPath="@UI.FieldGroup#ManufacturerData"/>
                        </Record>
                        <Record Type="UI.ReferenceFacet">
                            <PropertyValue Property="ID" String="FinancialData"/>
                            <PropertyValue Property="Label" String="Financial Data"/>
                            <PropertyValue Property="Target" AnnotationPath="@UI.FieldGroup#FinancialData"/>
                        </Record>
                        <Record Type="UI.ReferenceFacet">
                            <PropertyValue Property="ID" String="AddressData"/>
                            <PropertyValue Property="Label" String="Address Information"/>
                            <PropertyValue Property="Target" AnnotationPath="@UI.FieldGroup#AddressData"/>
                        </Record>
                        <Record Type="UI.ReferenceFacet">
                            <PropertyValue Property="ID" String="MaintenanceData"/>
                            <PropertyValue Property="Label" String="Maintenance Data"/>
                            <PropertyValue Property="Target" AnnotationPath="@UI.FieldGroup#MaintenanceData"/>
                        </Record>
                        <Record Type="UI.ReferenceFacet">
                            <PropertyValue Property="ID" String="SystemData"/>
                            <PropertyValue Property="Label" String="System Information"/>
                            <PropertyValue Property="Target" AnnotationPath="@UI.FieldGroup#SystemData"/>
                        </Record>
                    </Collection>
                </Annotation>

                <!-- General Information Field Group -->
                <Annotation Term="UI.FieldGroup" Qualifier="GeneralInformation">
                    <Record Type="UI.FieldGroupType">
                        <PropertyValue Property="Data">
                            <Collection>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="Equipment"/>
                                    <PropertyValue Property="Label" String="Equipment Number"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="EquipmentName"/>
                                    <PropertyValue Property="Label" String="Equipment Name"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="EquipmentCategory"/>
                                    <PropertyValue Property="Label" String="Equipment Category"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="TechnicalObjectType"/>
                                    <PropertyValue Property="Label" String="Technical Object Type"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="AuthorizationGroup"/>
                                    <PropertyValue Property="Label" String="Authorization Group"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="ValidityStartDate"/>
                                    <PropertyValue Property="Label" String="Valid From"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="ValidityEndDate"/>
                                    <PropertyValue Property="Label" String="Valid To"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="OperationStartDate"/>
                                    <PropertyValue Property="Label" String="Start-up Date"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="EquipmentEndOfUseDate"/>
                                    <PropertyValue Property="Label" String="End-of-Use Date"/>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>

                <!-- Technical Data Field Group -->
                <Annotation Term="UI.FieldGroup" Qualifier="TechnicalData">
                    <Record Type="UI.FieldGroupType">
                        <PropertyValue Property="Data">
                            <Collection>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="GrossWeight"/>
                                    <PropertyValue Property="Label" String="Gross Weight"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="GrossWeightUnit"/>
                                    <PropertyValue Property="Label" String="Weight Unit"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="SizeOrDimensionText"/>
                                    <PropertyValue Property="Label" String="Size/Dimension"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="InventoryNumber"/>
                                    <PropertyValue Property="Label" String="Inventory Number"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="Material"/>
                                    <PropertyValue Property="Label" String="Material"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="SerialNumber"/>
                                    <PropertyValue Property="Label" String="Serial Number"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="ConstructionMaterial"/>
                                    <PropertyValue Property="Label" String="Construction Material"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="TechnicalObjectSortCode"/>
                                    <PropertyValue Property="Label" String="Technical ID Number"/>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>

                <!-- Location & Organization Field Group -->
                <Annotation Term="UI.FieldGroup" Qualifier="LocationData">
                    <Record Type="UI.FieldGroupType">
                        <PropertyValue Property="Data">
                            <Collection>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="MaintenancePlant"/>
                                    <PropertyValue Property="Label" String="Maintenance Plant"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="AssetLocation"/>
                                    <PropertyValue Property="Label" String="Asset Location"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="AssetRoom"/>
                                    <PropertyValue Property="Label" String="Room"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="PlantSection"/>
                                    <PropertyValue Property="Label" String="Plant Section"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="WorkCenter"/>
                                    <PropertyValue Property="Label" String="Work Center"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="FunctionalLocationLabelName"/>
                                    <PropertyValue Property="Label" String="Functional Location"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="FunctionalLocationName"/>
                                    <PropertyValue Property="Label" String="Functional Location Description"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="SuperordinateEquipment"/>
                                    <PropertyValue Property="Label" String="Superordinate Equipment"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="EquipInstallationPositionNmbr"/>
                                    <PropertyValue Property="Label" String="Installation Position"/>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>

                <!-- Manufacturer Data Field Group -->
                <Annotation Term="UI.FieldGroup" Qualifier="ManufacturerData">
                    <Record Type="UI.FieldGroupType">
                        <PropertyValue Property="Data">
                            <Collection>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="AssetManufacturerName"/>
                                    <PropertyValue Property="Label" String="Manufacturer"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="ManufacturerPartTypeName"/>
                                    <PropertyValue Property="Label" String="Model Number"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="ManufacturerPartNmbr"/>
                                    <PropertyValue Property="Label" String="Manufacturer Part Number"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="ManufacturerSerialNumber"/>
                                    <PropertyValue Property="Label" String="Manufacturer Serial Number"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="ManufacturerCountry"/>
                                    <PropertyValue Property="Label" String="Country of Manufacture"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="ConstructionYear"/>
                                    <PropertyValue Property="Label" String="Construction Year"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="ConstructionMonth"/>
                                    <PropertyValue Property="Label" String="Construction Month"/>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>

                <!-- Financial Data Field Group -->
                <Annotation Term="UI.FieldGroup" Qualifier="FinancialData">
                    <Record Type="UI.FieldGroupType">
                        <PropertyValue Property="Data">
                            <Collection>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="AcquisitionValue"/>
                                    <PropertyValue Property="Label" String="Acquisition Value"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="Currency"/>
                                    <PropertyValue Property="Label" String="Currency"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="AcquisitionDate"/>
                                    <PropertyValue Property="Label" String="Acquisition Date"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="CompanyCode"/>
                                    <PropertyValue Property="Label" String="Company Code"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="BusinessArea"/>
                                    <PropertyValue Property="Label" String="Business Area"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="MasterFixedAsset"/>
                                    <PropertyValue Property="Label" String="Master Fixed Asset"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="FixedAsset"/>
                                    <PropertyValue Property="Label" String="Fixed Asset Sub-number"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="CostCenter"/>
                                    <PropertyValue Property="Label" String="Cost Center"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="ControllingArea"/>
                                    <PropertyValue Property="Label" String="Controlling Area"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="WBSElementExternalID"/>
                                    <PropertyValue Property="Label" String="WBS Element"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="SettlementOrder"/>
                                    <PropertyValue Property="Label" String="Settlement Order"/>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>

                <!-- Address Data Field Group -->
                <Annotation Term="UI.FieldGroup" Qualifier="AddressData">
                    <Record Type="UI.FieldGroupType">
                        <PropertyValue Property="Data">
                            <Collection>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="FormOfAddress"/>
                                    <PropertyValue Property="Label" String="Form of Address"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="BusinessPartnerName1"/>
                                    <PropertyValue Property="Label" String="Name 1"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="BusinessPartnerName2"/>
                                    <PropertyValue Property="Label" String="Name 2"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="StreetName"/>
                                    <PropertyValue Property="Label" String="Street"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="HouseNumber"/>
                                    <PropertyValue Property="Label" String="House Number"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="HouseNumberSupplementText"/>
                                    <PropertyValue Property="Label" String="House Number Supplement"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="Building"/>
                                    <PropertyValue Property="Label" String="Building"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="Floor"/>
                                    <PropertyValue Property="Label" String="Floor"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="RoomNumber"/>
                                    <PropertyValue Property="Label" String="Room Number"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="CityName"/>
                                    <PropertyValue Property="Label" String="City"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="PostalCode"/>
                                    <PropertyValue Property="Label" String="Postal Code"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="Region"/>
                                    <PropertyValue Property="Label" String="Region"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="Country"/>
                                    <PropertyValue Property="Label" String="Country"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="PhoneNumber"/>
                                    <PropertyValue Property="Label" String="Phone Number"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="FaxNumber"/>
                                    <PropertyValue Property="Label" String="Fax Number"/>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>

                <!-- Maintenance Data Field Group -->
                <Annotation Term="UI.FieldGroup" Qualifier="MaintenanceData">
                    <Record Type="UI.FieldGroupType">
                        <PropertyValue Property="Data">
                            <Collection>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="MaintenancePlanningPlant"/>
                                    <PropertyValue Property="Label" String="Planning Plant"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="MaintenancePlannerGroup"/>
                                    <PropertyValue Property="Label" String="Planner Group"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="MainWorkCenter"/>
                                    <PropertyValue Property="Label" String="Main Work Center"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="MainWorkCenterPlant"/>
                                    <PropertyValue Property="Label" String="Work Center Plant"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="CatalogProfile"/>
                                    <PropertyValue Property="Label" String="Catalog Profile"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="ABCIndicator"/>
                                    <PropertyValue Property="Label" String="ABC Indicator"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="MaintObjectFreeDefinedAttrib"/>
                                    <PropertyValue Property="Label" String="Sort Field"/>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>

                <!-- System Data Field Group -->
                <Annotation Term="UI.FieldGroup" Qualifier="SystemData">
                    <Record Type="UI.FieldGroupType">
                        <PropertyValue Property="Data">
                            <Collection>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="CreationDate"/>
                                    <PropertyValue Property="Label" String="Created On"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="CreatedByUser"/>
                                    <PropertyValue Property="Label" String="Created By"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="LastChangedByUser"/>
                                    <PropertyValue Property="Label" String="Last Changed By"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="LastChangeDateTime"/>
                                    <PropertyValue Property="Label" String="Last Change Date/Time"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="MaintObjectInternalID"/>
                                    <PropertyValue Property="Label" String="Object Internal ID"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="EquipmentOID"/>
                                    <PropertyValue Property="Label" String="Equipment Object Instance ID"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="UniqueItemIdentifier"/>
                                    <PropertyValue Property="Label" String="Unique Item Identifier"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="UniqueItemIdentifierStrucType"/>
                                    <PropertyValue Property="Label" String="UII Structure Type"/>
                                </Record>
                                <Record Type="UI.DataField">
                                    <PropertyValue Property="Value" Path="UniqueItemIdentifierRespPlant"/>
                                    <PropertyValue Property="Label" String="UII Responsible Plant"/>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>
        </Schema>
    </edmx:DataServices>
</edmx:Edmx>