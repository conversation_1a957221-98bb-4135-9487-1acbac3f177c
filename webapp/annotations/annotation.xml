<edmx:Edmx xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx" Version="4.0">
    <edmx:Reference Uri="https://sap.github.io/odata-vocabularies/vocabularies/Common.xml">
        <edmx:Include Namespace="com.sap.vocabularies.Common.v1" Alias="Common"/>
    </edmx:Reference>
    <edmx:Reference Uri="https://sap.github.io/odata-vocabularies/vocabularies/UI.xml">
        <edmx:Include Namespace="com.sap.vocabularies.UI.v1" Alias="UI"/>
    </edmx:Reference>
    <edmx:Reference Uri="https://sap.github.io/odata-vocabularies/vocabularies/Communication.xml">
        <edmx:Include Namespace="com.sap.vocabularies.Communication.v1" Alias="Communication"/>
    </edmx:Reference>
    <edmx:Reference Uri="/sap/opu/odata/sap/API_EQUIPMENT/$metadata">
        <edmx:Include Namespace="cds_api_equipment"/>
    </edmx:Reference>
    <edmx:DataServices>
        <Schema xmlns="http://docs.oasis-open.org/odata/ns/edm" Namespace="local">
            <Annotations Target="cds_api_equipment.EquipmentType">
                <Annotation Term="UI.LineItem">
                    <Collection>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="Equipment"/>
                            <PropertyValue Property="Label" String="Equipment Number"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="EquipmentName"/>
                            <PropertyValue Property="Label" String="Equipment Name"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="EquipmentCategory"/>
                            <PropertyValue Property="Label" String="Category"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="TechnicalObjectType"/>
                            <PropertyValue Property="Label" String="Object Type"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="MaintenancePlant"/>
                            <PropertyValue Property="Label" String="Plant"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="AssetLocation"/>
                            <PropertyValue Property="Label" String="Location"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="WorkCenter"/>
                            <PropertyValue Property="Label" String="Work Center"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="FunctionalLocationLabelName"/>
                            <PropertyValue Property="Label" String="Functional Location"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="AssetManufacturerName"/>
                            <PropertyValue Property="Label" String="Manufacturer"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="ManufacturerPartTypeName"/>
                            <PropertyValue Property="Label" String="Model"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="ManufacturerSerialNumber"/>
                            <PropertyValue Property="Label" String="Serial Number"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="OperationStartDate"/>
                            <PropertyValue Property="Label" String="Start-up Date"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="ValidityStartDate"/>
                            <PropertyValue Property="Label" String="Valid From"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="ValidityEndDate"/>
                            <PropertyValue Property="Label" String="Valid To"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="CreationDate"/>
                            <PropertyValue Property="Label" String="Created On"/>
                        </Record>
                        <Record Type="UI.DataField">
                            <PropertyValue Property="Value" Path="CreatedByUser"/>
                            <PropertyValue Property="Label" String="Created By"/>
                        </Record>
                    </Collection>
                </Annotation>
            </Annotations>
        </Schema>
    </edmx:DataServices>
</edmx:Edmx>