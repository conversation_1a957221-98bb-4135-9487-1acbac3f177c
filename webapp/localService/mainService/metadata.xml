<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="1.0" xmlns:edmx="http://schemas.microsoft.com/ado/2007/06/edmx" xmlns:m="http://schemas.microsoft.com/ado/2007/08/dataservices/metadata" xmlns:sap="http://www.sap.com/Protocols/SAPData">
    <edmx:Reference Uri="http://s4ha.unvired.io:8001/sap/opu/odata/iwfnd/catalogservice;v=2/Vocabularies(TechnicalName='%2FIWBEP%2FVOC_AGGREGATION',Version='0001',SAP__Origin='LOCAL')/$value" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
        <edmx:Include Namespace="Org.OData.Aggregation.V1" Alias="Aggregation"/>
    </edmx:Reference>
    <edmx:Reference Uri="http://s4ha.unvired.io:8001/sap/opu/odata/iwfnd/catalogservice;v=2/Vocabularies(TechnicalName='%2FIWBEP%2FVOC_ANALYTICS',Version='0001',SAP__Origin='LOCAL')/$value" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
        <edmx:Include Namespace="com.sap.vocabularies.Analytics.v1" Alias="Analytics"/>
    </edmx:Reference>
    <edmx:Reference Uri="http://s4ha.unvired.io:8001/sap/opu/odata/iwfnd/catalogservice;v=2/Vocabularies(TechnicalName='%2FIWBEP%2FVOC_CAPABILITIES',Version='0001',SAP__Origin='LOCAL')/$value" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
        <edmx:Include Namespace="Org.OData.Capabilities.V1" Alias="Capabilities"/>
    </edmx:Reference>
    <edmx:Reference Uri="http://s4ha.unvired.io:8001/sap/opu/odata/iwfnd/catalogservice;v=2/Vocabularies(TechnicalName='%2FIWBEP%2FVOC_CODELIST',Version='0001',SAP__Origin='LOCAL')/$value" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
        <edmx:Include Namespace="com.sap.vocabularies.CodeList.v1" Alias="SAP__CodeList"/>
    </edmx:Reference>
    <edmx:Reference Uri="http://s4ha.unvired.io:8001/sap/opu/odata/iwfnd/catalogservice;v=2/Vocabularies(TechnicalName='%2FIWBEP%2FVOC_COMMON',Version='0001',SAP__Origin='LOCAL')/$value" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
        <edmx:Include Namespace="com.sap.vocabularies.Common.v1" Alias="Common"/>
    </edmx:Reference>
    <edmx:Reference Uri="http://s4ha.unvired.io:8001/sap/opu/odata/iwfnd/catalogservice;v=2/Vocabularies(TechnicalName='%2FIWBEP%2FVOC_COMMUNICATION',Version='0001',SAP__Origin='LOCAL')/$value" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
        <edmx:Include Namespace="com.sap.vocabularies.Communication.v1" Alias="Communication"/>
    </edmx:Reference>
    <edmx:Reference Uri="http://s4ha.unvired.io:8001/sap/opu/odata/iwfnd/catalogservice;v=2/Vocabularies(TechnicalName='%2FIWBEP%2FVOC_MEASURES',Version='0001',SAP__Origin='LOCAL')/$value" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
        <edmx:Include Namespace="Org.OData.Measures.V1" Alias="Measures"/>
    </edmx:Reference>
    <edmx:Reference Uri="http://s4ha.unvired.io:8001/sap/opu/odata/iwfnd/catalogservice;v=2/Vocabularies(TechnicalName='%2FIWBEP%2FVOC_PERSONALDATA',Version='0001',SAP__Origin='LOCAL')/$value" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
        <edmx:Include Namespace="com.sap.vocabularies.PersonalData.v1" Alias="PersonalData"/>
    </edmx:Reference>
    <edmx:Reference Uri="http://s4ha.unvired.io:8001/sap/opu/odata/iwfnd/catalogservice;v=2/Vocabularies(TechnicalName='%2FIWBEP%2FVOC_UI',Version='0001',SAP__Origin='LOCAL')/$value" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
        <edmx:Include Namespace="com.sap.vocabularies.UI.v1" Alias="UI"/>
    </edmx:Reference>
    <edmx:Reference Uri="http://s4ha.unvired.io:8001/sap/opu/odata/iwfnd/catalogservice;v=2/Vocabularies(TechnicalName='%2FIWBEP%2FVOC_VALIDATION',Version='0001',SAP__Origin='LOCAL')/$value" xmlns:edmx="http://docs.oasis-open.org/odata/ns/edmx">
        <edmx:Include Namespace="Org.OData.Validation.V1" Alias="Validation"/>
    </edmx:Reference>
    <edmx:DataServices m:DataServiceVersion="2.0">
        <Schema Namespace="cds_api_equipment" xml:lang="en" sap:schema-version="1" xmlns="http://schemas.microsoft.com/ado/2008/09/edm">
            <EntityType Name="EquipmentType" sap:label="Equipment" sap:content-version="1">
                <Key>
                    <PropertyRef Name="Equipment"/>
                    <PropertyRef Name="ValidityEndDate"/>
                </Key>
                <Property Name="Equipment" Type="Edm.String" Nullable="false" MaxLength="18" sap:display-format="UpperCase" sap:label="Equipment" sap:quickinfo="Equipment Number" sap:updatable="false"/>
                <Property Name="ValidityEndDate" Type="Edm.DateTime" Nullable="false" Precision="0" sap:display-format="Date" sap:label="Valid To" sap:quickinfo="Valid To Date" sap:creatable="false" sap:updatable="false"/>
                <Property Name="ValidityEndTime" Type="Edm.Time" Precision="0" sap:label="Time" sap:quickinfo="Equipment usage period time stamp" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipmentValidityEndDateTime" Type="Edm.DateTimeOffset" Precision="0" sap:creatable="false" sap:updatable="false"/>
                <Property Name="ValidityStartDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:label="Valid From" sap:quickinfo="Valid-From Date"/>
                <Property Name="EquipmentOID" Type="Edm.String" MaxLength="128" sap:label="Equip Obj Instce ID" sap:quickinfo="Object Instance ID of an Equipment"/>
                <Property Name="EquipmentName" Type="Edm.String" MaxLength="40" sap:label="Object Description" sap:quickinfo="Description of technical object"/>
                <Property Name="EquipmentCategory" Type="Edm.String" MaxLength="1" sap:display-format="UpperCase" sap:label="Equipment category"/>
                <Property Name="TechnicalObjectType" Type="Edm.String" MaxLength="10" sap:display-format="UpperCase" sap:label="Object Type" sap:quickinfo="Type of Technical Object"/>
                <Property Name="AuthorizationGroup" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="AuthorizGroup" sap:quickinfo="Technical Object Authorization Group"/>
                <Property Name="GrossWeight" Type="Edm.Decimal" Precision="13" Scale="3" sap:unit="GrossWeightUnit" sap:label="Weight of object"/>
                <Property Name="GrossWeightUnit" Type="Edm.String" MaxLength="3" sap:label="Unit of weight" sap:semantics="unit-of-measure"/>
                <Property Name="SizeOrDimensionText" Type="Edm.String" MaxLength="18" sap:display-format="UpperCase" sap:label="Size/dimension"/>
                <Property Name="InventoryNumber" Type="Edm.String" MaxLength="25" sap:display-format="UpperCase" sap:label="Inventory Number" sap:quickinfo="Inventory number"/>
                <Property Name="OperationStartDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:label="Start-up date" sap:quickinfo="Start-up Date of the Technical Object"/>
                <Property Name="AcquisitionValue" Type="Edm.Decimal" Precision="13" Scale="3" sap:unit="Currency" sap:variable-scale="true" sap:label="Acquisition Value"/>
                <Property Name="Currency" Type="Edm.String" MaxLength="5" sap:display-format="UpperCase" sap:label="Currency" sap:quickinfo="Currency Key" sap:semantics="currency-code"/>
                <Property Name="AcquisitionDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:label="Acquisition date"/>
                <Property Name="AssetManufacturerName" Type="Edm.String" MaxLength="30" sap:label="Manufacturer" sap:quickinfo="Manufacturer of Asset"/>
                <Property Name="ManufacturerPartTypeName" Type="Edm.String" MaxLength="20" sap:label="Model number" sap:quickinfo="Manufacturer model number"/>
                <Property Name="ManufacturerCountry" Type="Edm.String" MaxLength="3" sap:display-format="UpperCase" sap:label="Mfr Ctry/Reg" sap:quickinfo="Country/Region of Manufacture"/>
                <Property Name="ConstructionYear" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Construction year" sap:quickinfo="Year of construction"/>
                <Property Name="ConstructionMonth" Type="Edm.String" MaxLength="2" sap:display-format="UpperCase" sap:label="Construction month" sap:quickinfo="Month of construction"/>
                <Property Name="ManufacturerPartNmbr" Type="Edm.String" MaxLength="30" sap:display-format="UpperCase" sap:label="ManufactPartNo." sap:quickinfo="Manufacturer part number"/>
                <Property Name="ManufacturerSerialNumber" Type="Edm.String" MaxLength="30" sap:display-format="UpperCase" sap:label="Manuf. Serial Number" sap:quickinfo="Manufacturer's Serial Number"/>
                <Property Name="EquipmentEndOfUseDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:label="End-of-Use Date" sap:quickinfo="End-of-Use Date of the Technical Object"/>
                <Property Name="MaintenancePlant" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Maintenance Plant"/>
                <Property Name="AssetLocation" Type="Edm.String" MaxLength="10" sap:display-format="UpperCase" sap:label="Location" sap:quickinfo="Location of maintenance object"/>
                <Property Name="AssetRoom" Type="Edm.String" MaxLength="8" sap:display-format="UpperCase" sap:label="Room"/>
                <Property Name="PlantSection" Type="Edm.String" MaxLength="3" sap:display-format="UpperCase" sap:label="Plant Section"/>
                <Property Name="WorkCenter" Type="Edm.String" MaxLength="8" sap:display-format="UpperCase" sap:label="Work Center"/>
                <Property Name="WorkCenterInternalID" Type="Edm.String" MaxLength="8" sap:display-format="NonNegative" sap:label="PP work center" sap:quickinfo="Object ID of PP work center" sap:creatable="false" sap:updatable="false"/>
                <Property Name="WorkCenterPlant" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Plant" sap:creatable="false" sap:updatable="false"/>
                <Property Name="ABCIndicator" Type="Edm.String" MaxLength="1" sap:display-format="UpperCase" sap:label="ABC Indicator" sap:quickinfo="ABC Indicator for Technical Object"/>
                <Property Name="MaintObjectFreeDefinedAttrib" Type="Edm.String" MaxLength="30" sap:display-format="UpperCase" sap:label="Sort Field"/>
                <Property Name="FormOfAddress" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Title Key" sap:quickinfo="Form-of-Address Key"/>
                <Property Name="BusinessPartnerName1" Type="Edm.String" MaxLength="40" sap:label="Name" sap:quickinfo="Name 1"/>
                <Property Name="BusinessPartnerName2" Type="Edm.String" MaxLength="40" sap:label="Name 2"/>
                <Property Name="CityName" Type="Edm.String" MaxLength="40" sap:label="City"/>
                <Property Name="HouseNumber" Type="Edm.String" MaxLength="10" sap:label="House Number"/>
                <Property Name="HouseNumberSupplementText" Type="Edm.String" MaxLength="10" sap:label="Supplement" sap:quickinfo="House number supplement"/>
                <Property Name="Building" Type="Edm.String" MaxLength="20" sap:label="Building Code" sap:quickinfo="Building (Number or Code)"/>
                <Property Name="Floor" Type="Edm.String" MaxLength="10" sap:label="Floor" sap:quickinfo="Floor in building"/>
                <Property Name="RoomNumber" Type="Edm.String" MaxLength="10" sap:label="Room Number" sap:quickinfo="Room or Apartment Number"/>
                <Property Name="PostalCode" Type="Edm.String" MaxLength="10" sap:display-format="UpperCase" sap:label="Postal Code" sap:quickinfo="City postal code"/>
                <Property Name="StreetName" Type="Edm.String" MaxLength="60" sap:label="Street"/>
                <Property Name="Region" Type="Edm.String" MaxLength="3" sap:display-format="UpperCase" sap:label="Region" sap:quickinfo="Region (State, Province, County)"/>
                <Property Name="Country" Type="Edm.String" MaxLength="3" sap:display-format="UpperCase" sap:label="Country/Region Key"/>
                <Property Name="PhoneNumber" Type="Edm.String" MaxLength="30" sap:display-format="UpperCase" sap:label="Telephone" sap:quickinfo="Telephone No.: Dialing Code and Number"/>
                <Property Name="FaxNumber" Type="Edm.String" MaxLength="30" sap:display-format="UpperCase" sap:label="Fax" sap:quickinfo="Fax Number: Dialing Code and Number"/>
                <Property Name="CompanyCode" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Company Code"/>
                <Property Name="BusinessArea" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Business Area"/>
                <Property Name="MasterFixedAsset" Type="Edm.String" MaxLength="12" sap:display-format="UpperCase" sap:label="Asset" sap:quickinfo="Main Asset Number"/>
                <Property Name="FixedAsset" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Sub-number" sap:quickinfo="Asset Subnumber"/>
                <Property Name="CostCenter" Type="Edm.String" MaxLength="10" sap:display-format="UpperCase" sap:label="Cost Center"/>
                <Property Name="ControllingArea" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Controlling Area"/>
                <Property Name="WBSElementExternalID" Type="Edm.String" MaxLength="24" sap:display-format="UpperCase" sap:label="WBS Element" sap:quickinfo="Work Breakdown Structure Element (WBS Element) Edited"/>
                <Property Name="SettlementOrder" Type="Edm.String" MaxLength="12" sap:display-format="UpperCase" sap:label="Settlement Order" sap:quickinfo="Settlement order"/>
                <Property Name="MaintenancePlanningPlant" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Planning Plant" sap:quickinfo="Maintenance Planning Plant"/>
                <Property Name="MaintenancePlannerGroup" Type="Edm.String" MaxLength="3" sap:display-format="UpperCase" sap:label="Maint. Planner Group" sap:quickinfo="Planner Group for Customer Service and Plant Maintenance"/>
                <Property Name="MainWorkCenter" Type="Edm.String" MaxLength="8" sap:display-format="UpperCase" sap:label="Main Work Center" sap:quickinfo="Main work center for maintenance tasks"/>
                <Property Name="MainWorkCenterInternalID" Type="Edm.String" MaxLength="8" sap:display-format="NonNegative" sap:label="Work Center" sap:quickinfo="Object ID of the Work Center" sap:creatable="false" sap:updatable="false"/>
                <Property Name="MainWorkCenterPlant" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Plant for WorkCenter" sap:quickinfo="Plant associated with main work center"/>
                <Property Name="CatalogProfile" Type="Edm.String" MaxLength="9" sap:display-format="UpperCase" sap:label="Catalog Profile"/>
                <Property Name="FunctionalLocation" Type="Edm.String" MaxLength="30" sap:display-format="UpperCase" sap:label="Object ID" sap:quickinfo="Object ID of Functional Location or Equipment" sap:creatable="false" sap:updatable="false"/>
                <Property Name="FunctionalLocationLabelName" Type="Edm.String" MaxLength="40" sap:display-format="UpperCase" sap:label="Functional Location" sap:quickinfo="Functional Location Label"/>
                <Property Name="FunctionalLocationName" Type="Edm.String" MaxLength="40" sap:label="FunctLocDescrip." sap:quickinfo="Description of functional location" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipInstallationPositionNmbr" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Position" sap:quickinfo="Equipment position at InstallLoc (Superior Equip./FunctLoc)"/>
                <Property Name="SuperordinateEquipment" Type="Edm.String" MaxLength="18" sap:display-format="UpperCase" sap:label="Superord. Equipment" sap:quickinfo="Superordinate Equipment"/>
                <Property Name="TechnicalObjectSortCode" Type="Edm.String" MaxLength="25" sap:display-format="UpperCase" sap:label="Technical IdentNo." sap:quickinfo="Technical identification number"/>
                <Property Name="ConstructionMaterial" Type="Edm.String" MaxLength="18" sap:display-format="UpperCase" sap:label="Construction Type" sap:quickinfo="Construction type material of the object"/>
                <Property Name="Material" Type="Edm.String" MaxLength="18" sap:display-format="UpperCase" sap:label="Material" sap:quickinfo="Material Number"/>
                <Property Name="SerialNumber" Type="Edm.String" MaxLength="18" sap:display-format="UpperCase" sap:label="Serial Number"/>
                <Property Name="UniqueItemIdentifier" Type="Edm.String" MaxLength="72" sap:display-format="UpperCase" sap:label="UII" sap:quickinfo="Unique Item Identifier"/>
                <Property Name="UniqueItemIdentifierStrucType" Type="Edm.String" MaxLength="10" sap:display-format="UpperCase" sap:label="IUID Type" sap:quickinfo="Structure Type of UII"/>
                <Property Name="UniqueItemIdentifierRespPlant" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Resp. Plant UII" sap:quickinfo="Plant Responsible for UII"/>
                <Property Name="SalesOrganization" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Sales Organization"/>
                <Property Name="DistributionChannel" Type="Edm.String" MaxLength="2" sap:display-format="UpperCase" sap:label="Distribution Channel"/>
                <Property Name="OrganizationDivision" Type="Edm.String" MaxLength="2" sap:display-format="UpperCase" sap:label="Division"/>
                <Property Name="SalesGroup" Type="Edm.String" MaxLength="3" sap:display-format="UpperCase" sap:label="Sales Group"/>
                <Property Name="SalesOffice" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Sales Office"/>
                <Property Name="CreationDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:label="Created On" sap:quickinfo="Record Created On" sap:creatable="false" sap:updatable="false"/>
                <Property Name="CreatedByUser" Type="Edm.String" MaxLength="12" sap:display-format="UpperCase" sap:label="Created By" sap:quickinfo="Name of Person Responsible for Creating the Object" sap:creatable="false" sap:updatable="false"/>
                <Property Name="LastChangedByUser" Type="Edm.String" MaxLength="12" sap:display-format="UpperCase" sap:label="Changed By" sap:quickinfo="Name of Person Who Changed Object" sap:creatable="false" sap:updatable="false"/>
                <Property Name="LastChangeDateTime" Type="Edm.DateTimeOffset" Precision="0" sap:label="Short Time Stamp" sap:quickinfo="UTC Time Stamp in Short Form (YYYYMMDDhhmmss)" sap:creatable="false" sap:updatable="false"/>
                <Property Name="NextEquipUsagePeriodSqncNmbr" Type="Edm.String" MaxLength="3" sap:display-format="NonNegative" sap:label="NxtUsagePeriodNo." sap:quickinfo="Number of next EquipUsagePeriod on same day" sap:creatable="false" sap:updatable="false"/>
                <Property Name="MaintObjectLocAcctAssgmtNmbr" Type="Edm.String" MaxLength="12" sap:display-format="UpperCase" sap:label="Loc/Acct Assignment" sap:quickinfo="Location and account assignment for technical object" sap:creatable="false" sap:updatable="false"/>
                <Property Name="MaintObjectInternalID" Type="Edm.String" MaxLength="22" sap:display-format="UpperCase" sap:label="Object number" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipUsagePeriodSequenceNumber" Type="Edm.String" MaxLength="3" sap:display-format="NonNegative" sap:label="UsgePerConsecNo." sap:quickinfo="Consecutive numbering of EquipUsagePeriods on same day" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipmentIsAtCustomer" Type="Edm.Boolean" sap:label="Checkbox" sap:heading="" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipmentIsAvailable" Type="Edm.Boolean" sap:label="Checkbox" sap:heading="" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipmentIsInWarehouse" Type="Edm.Boolean" sap:label="Checkbox" sap:heading="" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipmentIsAssignedToDelivery" Type="Edm.Boolean" sap:label="Checkbox" sap:heading="" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipmentIsMarkedForDeletion" Type="Edm.Boolean" sap:label="Checkbox" sap:heading="" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipmentIsInstalled" Type="Edm.Boolean" sap:label="Checkbox" sap:heading="" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipIsAllocToSuperiorEquip" Type="Edm.Boolean" sap:label="Checkbox" sap:heading="" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipmentIsInactive" Type="Edm.Boolean" sap:label="Checkbox" sap:heading="" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipHasSubOrdinateEquipment" Type="Edm.String" MaxLength="1"/>
                <NavigationProperty Name="to_Classification" Relationship="cds_api_equipment.assoc_0465B5D110F99F644D875C9C955D4F32" FromRole="FromRole_assoc_0465B5D110F99F644D875C9C955D4F32" ToRole="ToRole_assoc_0465B5D110F99F644D875C9C955D4F32"/>
                <NavigationProperty Name="to_LongText" Relationship="cds_api_equipment.assoc_DA857AA25A191131134DCDEA3428DC9F" FromRole="FromRole_assoc_DA857AA25A191131134DCDEA3428DC9F" ToRole="ToRole_assoc_DA857AA25A191131134DCDEA3428DC9F"/>
                <NavigationProperty Name="to_Partner" Relationship="cds_api_equipment.assoc_8C45CFE9D5910E9A0B09F1A6F54222DD" FromRole="FromRole_assoc_8C45CFE9D5910E9A0B09F1A6F54222DD" ToRole="ToRole_assoc_8C45CFE9D5910E9A0B09F1A6F54222DD"/>
                <NavigationProperty Name="to_Warranty" Relationship="cds_api_equipment.assoc_9565F4FC391DC57A0EC7360AC03FA518" FromRole="FromRole_assoc_9565F4FC391DC57A0EC7360AC03FA518" ToRole="ToRole_assoc_9565F4FC391DC57A0EC7360AC03FA518"/>
            </EntityType>
            <EntityType Name="EquipClassCharacteristicValueType" sap:label="Equipment Characteristic Values" sap:content-version="1">
                <Key>
                    <PropertyRef Name="Equipment"/>
                    <PropertyRef Name="ClassType"/>
                    <PropertyRef Name="CharcValuePositionNumber"/>
                    <PropertyRef Name="CharacteristicInternalID"/>
                </Key>
                <Property Name="Equipment" Type="Edm.String" Nullable="false" MaxLength="18" sap:display-format="UpperCase" sap:label="Equipment" sap:quickinfo="Equipment Number" sap:updatable="false"/>
                <Property Name="ClassType" Type="Edm.String" Nullable="false" MaxLength="3" sap:display-format="UpperCase" sap:label="Class Type" sap:updatable="false"/>
                <Property Name="CharcValuePositionNumber" Type="Edm.String" Nullable="false" MaxLength="3" sap:display-format="NonNegative" sap:label="Counter" sap:quickinfo="Characteristic value counter" sap:updatable="false"/>
                <Property Name="CharacteristicInternalID" Type="Edm.String" Nullable="false" MaxLength="10" sap:display-format="NonNegative" sap:label="Characteristic Internal ID" sap:creatable="false" sap:updatable="false"/>
                <Property Name="Characteristic" Type="Edm.String" MaxLength="30" sap:display-format="UpperCase" sap:label="Characteristic Name"/>
                <Property Name="CharcDataType" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Format" sap:quickinfo="Data type of characteristic"/>
                <Property Name="ClassInternalID" Type="Edm.String" Nullable="false" MaxLength="10" sap:display-format="NonNegative" sap:label="Internal class no." sap:quickinfo="Internal Class Number" sap:updatable="false"/>
                <Property Name="KeyDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:creatable="false" sap:updatable="false"/>
                <Property Name="ChangeNumber" Type="Edm.String" MaxLength="12" sap:display-format="UpperCase" sap:label="Change Number"/>
                <Property Name="CharcValueDependency" Type="Edm.String" MaxLength="1" sap:display-format="UpperCase" sap:label="Code" sap:quickinfo="Code for value dependency"/>
                <Property Name="CharcValue" Type="Edm.String" MaxLength="70" sap:label="Characteristic Value"/>
                <Property Name="CharcFromNumericValue" Type="Edm.Double" sap:label="Value from" sap:quickinfo="Internal floating point from"/>
                <Property Name="CharcFromNumericValueUnit" Type="Edm.String" MaxLength="3" sap:label="Internal UoM" sap:quickinfo="Unit of Measurement" sap:semantics="unit-of-measure"/>
                <Property Name="CharcToNumericValue" Type="Edm.Double" sap:label="Value to" sap:quickinfo="Internal floating point value to"/>
                <Property Name="CharcToNumericValueUnit" Type="Edm.String" MaxLength="3" sap:label="Internal UoM" sap:quickinfo="Unit of Measurement" sap:semantics="unit-of-measure"/>
                <Property Name="CharcFromDecimalValue" Type="Edm.Decimal" Precision="31" Scale="14" sap:label="LowrBndry Nmrc" sap:quickinfo="Lower Boundary for Numeric Field"/>
                <Property Name="CharcToDecimalValue" Type="Edm.Decimal" Precision="31" Scale="14" sap:label="UprBndry Nmrc" sap:quickinfo="Upper Boundary for Numeric Field"/>
                <Property Name="CharcFromAmount" Type="Edm.Decimal" Precision="23" Scale="3" sap:unit="Currency" sap:variable-scale="true" sap:label="LowrBndry Crcy" sap:quickinfo="Lower Boundary for Currency Field"/>
                <Property Name="CharcToAmount" Type="Edm.Decimal" Precision="23" Scale="3" sap:unit="Currency" sap:variable-scale="true" sap:label="UprBndry Crcy" sap:quickinfo="Upper Boundary for Currency Field"/>
                <Property Name="Currency" Type="Edm.String" MaxLength="5" sap:display-format="UpperCase" sap:label="Currency" sap:quickinfo="Currency Key" sap:semantics="currency-code"/>
                <Property Name="CharcFromDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:label="LowrBndry Date" sap:quickinfo="Lower Boundary for Date-Interval"/>
                <Property Name="CharcToDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:label="UprBndry Date" sap:quickinfo="Upper Boundary for Date-Interval"/>
                <Property Name="CharcFromTime" Type="Edm.Time" Precision="0" sap:label="LowrBndry Time" sap:quickinfo="Lower Boundary for Time-Interval"/>
                <Property Name="CharcToTime" Type="Edm.Time" Precision="0" sap:label="UprBndry Time" sap:quickinfo="Upper Boundary for Time-Interval"/>
                <NavigationProperty Name="to_Equipment" Relationship="cds_api_equipment.assoc_BE0918A080660F98CF0DDB56BDB8CFE3" FromRole="FromRole_assoc_BE0918A080660F98CF0DDB56BDB8CFE3" ToRole="ToRole_assoc_BE0918A080660F98CF0DDB56BDB8CFE3"/>
                <NavigationProperty Name="to_Characteristic" Relationship="cds_api_equipment.assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF" FromRole="ToRole_assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF" ToRole="FromRole_assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF"/>
            </EntityType>
            <EntityType Name="EquipmentClassType" sap:label="Equipment Class" sap:content-version="1">
                <Key>
                    <PropertyRef Name="Equipment"/>
                    <PropertyRef Name="ClassInternalID"/>
                    <PropertyRef Name="ClassType"/>
                </Key>
                <Property Name="Equipment" Type="Edm.String" Nullable="false" MaxLength="18" sap:display-format="UpperCase" sap:label="Equipment" sap:quickinfo="Equipment Number" sap:updatable="false"/>
                <Property Name="ClassInternalID" Type="Edm.String" Nullable="false" MaxLength="10" sap:display-format="NonNegative" sap:label="Internal class no." sap:quickinfo="Internal Class Number" sap:updatable="false"/>
                <Property Name="ClassType" Type="Edm.String" Nullable="false" MaxLength="3" sap:display-format="UpperCase" sap:label="Class Type" sap:updatable="false"/>
                <Property Name="KeyDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:creatable="false" sap:updatable="false"/>
                <Property Name="ChangeNumber" Type="Edm.String" MaxLength="12" sap:display-format="UpperCase" sap:label="Change Number"/>
                <Property Name="ClfnStatus" Type="Edm.String" MaxLength="1" sap:display-format="UpperCase" sap:label="Status" sap:quickinfo="Classification status"/>
                <Property Name="ClassPositionNumber" Type="Edm.Int16" sap:label="Pos." sap:quickinfo="Sort position"/>
                <Property Name="ClassIsStandardClass" Type="Edm.String" MaxLength="1" sap:display-format="UpperCase" sap:label="Standard class" sap:quickinfo="Indicator: Standard Class"/>
                <Property Name="ClfnObjectInternalID" Type="Edm.String" MaxLength="18" sap:display-format="NonNegative" sap:label="Internal object no." sap:quickinfo="Configuration (internal object number)" sap:creatable="false" sap:updatable="false"/>
                <Property Name="ValidityEndDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:label="Valid to" sap:quickinfo="Valid-to date" sap:creatable="false" sap:updatable="false"/>
                <Property Name="Class" Type="Edm.String" MaxLength="18" sap:display-format="UpperCase" sap:label="Class" sap:quickinfo="Class number" sap:creatable="false" sap:updatable="false"/>
                <NavigationProperty Name="to_Equipment" Relationship="cds_api_equipment.assoc_0465B5D110F99F644D875C9C955D4F32" FromRole="ToRole_assoc_0465B5D110F99F644D875C9C955D4F32" ToRole="FromRole_assoc_0465B5D110F99F644D875C9C955D4F32"/>
                <NavigationProperty Name="to_Characteristic" Relationship="cds_api_equipment.assoc_68DE9F74E96A8F271658D66C1CD7D9A3" FromRole="FromRole_assoc_68DE9F74E96A8F271658D66C1CD7D9A3" ToRole="ToRole_assoc_68DE9F74E96A8F271658D66C1CD7D9A3"/>
            </EntityType>
            <EntityType Name="EquipmentClassCharacteristicType" sap:label="Equipment Class Characteristics" sap:content-version="1">
                <Key>
                    <PropertyRef Name="Equipment"/>
                    <PropertyRef Name="ClassInternalID"/>
                    <PropertyRef Name="CharacteristicInternalID"/>
                </Key>
                <Property Name="Equipment" Type="Edm.String" Nullable="false" MaxLength="18" sap:display-format="UpperCase" sap:label="Equipment" sap:quickinfo="Equipment Number" sap:updatable="false"/>
                <Property Name="ClassInternalID" Type="Edm.String" Nullable="false" MaxLength="10" sap:display-format="NonNegative" sap:label="Internal class no." sap:quickinfo="Internal Class Number" sap:updatable="false"/>
                <Property Name="CharacteristicInternalID" Type="Edm.String" Nullable="false" MaxLength="10" sap:display-format="NonNegative" sap:label="Characteristic Internal ID" sap:updatable="false"/>
                <Property Name="Characteristic" Type="Edm.String" MaxLength="30" sap:display-format="UpperCase" sap:label="Characteristic Name"/>
                <Property Name="CharcDataType" Type="Edm.String" MaxLength="4" sap:display-format="UpperCase" sap:label="Format" sap:quickinfo="Data type of characteristic"/>
                <Property Name="KeyDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:creatable="false" sap:updatable="false"/>
                <Property Name="ChangeNumber" Type="Edm.String" MaxLength="12" sap:display-format="UpperCase" sap:label="Change Number"/>
                <Property Name="ClassType" Type="Edm.String" MaxLength="3" sap:display-format="UpperCase" sap:label="Class Type" sap:updatable="false"/>
                <Property Name="Class" Type="Edm.String" MaxLength="18" sap:display-format="UpperCase" sap:label="Class" sap:quickinfo="Class number" sap:creatable="false" sap:updatable="false"/>
                <NavigationProperty Name="to_EquipmentClass" Relationship="cds_api_equipment.assoc_68DE9F74E96A8F271658D66C1CD7D9A3" FromRole="ToRole_assoc_68DE9F74E96A8F271658D66C1CD7D9A3" ToRole="FromRole_assoc_68DE9F74E96A8F271658D66C1CD7D9A3"/>
                <NavigationProperty Name="to_Equipment" Relationship="cds_api_equipment.assoc_CFFEFD349364B3968B02807B151D07F0" FromRole="FromRole_assoc_CFFEFD349364B3968B02807B151D07F0" ToRole="ToRole_assoc_CFFEFD349364B3968B02807B151D07F0"/>
                <NavigationProperty Name="to_Value" Relationship="cds_api_equipment.assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF" FromRole="FromRole_assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF" ToRole="ToRole_assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF"/>
            </EntityType>
            <EntityType Name="EquipmentLongTextType" sap:label="Equipment Long Text" sap:content-version="1">
                <Key>
                    <PropertyRef Name="Equipment"/>
                </Key>
                <Property Name="Equipment" Type="Edm.String" Nullable="false" MaxLength="18" sap:display-format="UpperCase" sap:label="Equipment" sap:quickinfo="Equipment Number" sap:updatable="false"/>
                <Property Name="TextObjectKey" Type="Edm.String" MaxLength="18" sap:display-format="UpperCase" sap:label="Equipment" sap:quickinfo="Equipment Number" sap:creatable="false" sap:updatable="false"/>
                <Property Name="Language" Type="Edm.String" MaxLength="2" sap:label="Language Key" sap:creatable="false" sap:updatable="false"/>
                <Property Name="EquipmentLongText" Type="Edm.String" sap:label="Long Text"/>
                <NavigationProperty Name="to_Equipment" Relationship="cds_api_equipment.assoc_DA857AA25A191131134DCDEA3428DC9F" FromRole="ToRole_assoc_DA857AA25A191131134DCDEA3428DC9F" ToRole="FromRole_assoc_DA857AA25A191131134DCDEA3428DC9F"/>
            </EntityType>
            <EntityType Name="EquipmentPartnerType" sap:label="Equipment Partner Functions" sap:content-version="1">
                <Key>
                    <PropertyRef Name="Equipment"/>
                    <PropertyRef Name="PartnerFunction"/>
                    <PropertyRef Name="EquipmentPartnerObjectNmbr"/>
                </Key>
                <Property Name="Equipment" Type="Edm.String" Nullable="false" MaxLength="18" sap:display-format="UpperCase" sap:label="Equipment" sap:quickinfo="Equipment Number" sap:updatable="false"/>
                <Property Name="PartnerFunction" Type="Edm.String" Nullable="false" MaxLength="2" sap:display-format="UpperCase" sap:label="Partner Function" sap:updatable="false"/>
                <Property Name="EquipmentPartnerObjectNmbr" Type="Edm.String" Nullable="false" MaxLength="6" sap:display-format="NonNegative" sap:label="Counter" sap:quickinfo="Counter for differentiation 6-digit" sap:updatable="false"/>
                <Property Name="Partner" Type="Edm.String" MaxLength="12" sap:display-format="UpperCase" sap:label="Partner"/>
                <Property Name="CreatedByUser" Type="Edm.String" MaxLength="12" sap:display-format="UpperCase" sap:label="Created By" sap:quickinfo="Name of Person Responsible for Creating the Object" sap:creatable="false" sap:updatable="false"/>
                <Property Name="CreationDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:label="Created On" sap:quickinfo="Record Created On" sap:creatable="false" sap:updatable="false"/>
                <NavigationProperty Name="to_Equipment" Relationship="cds_api_equipment.assoc_8C45CFE9D5910E9A0B09F1A6F54222DD" FromRole="ToRole_assoc_8C45CFE9D5910E9A0B09F1A6F54222DD" ToRole="FromRole_assoc_8C45CFE9D5910E9A0B09F1A6F54222DD"/>
            </EntityType>
            <EntityType Name="EquipmentWarrantyType" sap:label="Equipment Warranty" sap:content-version="1">
                <Key>
                    <PropertyRef Name="Equipment"/>
                    <PropertyRef Name="WarrantyType"/>
                </Key>
                <Property Name="Equipment" Type="Edm.String" Nullable="false" MaxLength="18" sap:display-format="UpperCase" sap:label="Equipment" sap:quickinfo="Equipment Number" sap:updatable="false"/>
                <Property Name="WarrantyType" Type="Edm.String" Nullable="false" MaxLength="1" sap:display-format="UpperCase" sap:label="Warranty type" sap:updatable="false"/>
                <Property Name="MasterWarranty" Type="Edm.String" MaxLength="20" sap:display-format="UpperCase" sap:label="Master warranty" sap:quickinfo="Master warranty number"/>
                <Property Name="WarrantyStartDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:label="Warranty" sap:quickinfo="Warranty Date"/>
                <Property Name="WarrantyEndDate" Type="Edm.DateTime" Precision="0" sap:display-format="Date" sap:label="Warranty end date" sap:quickinfo="Date on which the warranty ends"/>
                <Property Name="WrntyIsInhtdFromSuperiorObject" Type="Edm.String" MaxLength="1" sap:display-format="UpperCase" sap:label="InheritWarranty" sap:quickinfo="Indicator, Whether Technical Object Should Inherit Warranty"/>
                <Property Name="WrntyIsPassedOnToChildObject" Type="Edm.String" MaxLength="1" sap:display-format="UpperCase" sap:label="Pass on warrnty" sap:quickinfo="Indicator: Pass on Warranty"/>
                <NavigationProperty Name="to_Equipment" Relationship="cds_api_equipment.assoc_9565F4FC391DC57A0EC7360AC03FA518" FromRole="ToRole_assoc_9565F4FC391DC57A0EC7360AC03FA518" ToRole="FromRole_assoc_9565F4FC391DC57A0EC7360AC03FA518"/>
            </EntityType>
            <ComplexType Name="DummyFunctionImportResult">
                <Property Name="IsInvalid" Type="Edm.Boolean" sap:label="TRUE"/>
            </ComplexType>
            <Association Name="assoc_DA857AA25A191131134DCDEA3428DC9F" sap:content-version="1">
                <End Type="cds_api_equipment.EquipmentType" Multiplicity="1" Role="FromRole_assoc_DA857AA25A191131134DCDEA3428DC9F">
                    <OnDelete Action="Cascade"/>
                </End>
                <End Type="cds_api_equipment.EquipmentLongTextType" Multiplicity="1" Role="ToRole_assoc_DA857AA25A191131134DCDEA3428DC9F"/>
            </Association>
            <Association Name="assoc_8C45CFE9D5910E9A0B09F1A6F54222DD" sap:content-version="1">
                <End Type="cds_api_equipment.EquipmentType" Multiplicity="1" Role="FromRole_assoc_8C45CFE9D5910E9A0B09F1A6F54222DD">
                    <OnDelete Action="Cascade"/>
                </End>
                <End Type="cds_api_equipment.EquipmentPartnerType" Multiplicity="*" Role="ToRole_assoc_8C45CFE9D5910E9A0B09F1A6F54222DD"/>
            </Association>
            <Association Name="assoc_9565F4FC391DC57A0EC7360AC03FA518" sap:content-version="1">
                <End Type="cds_api_equipment.EquipmentType" Multiplicity="1" Role="FromRole_assoc_9565F4FC391DC57A0EC7360AC03FA518">
                    <OnDelete Action="Cascade"/>
                </End>
                <End Type="cds_api_equipment.EquipmentWarrantyType" Multiplicity="*" Role="ToRole_assoc_9565F4FC391DC57A0EC7360AC03FA518"/>
            </Association>
            <Association Name="assoc_0465B5D110F99F644D875C9C955D4F32" sap:content-version="1">
                <End Type="cds_api_equipment.EquipmentType" Multiplicity="1" Role="FromRole_assoc_0465B5D110F99F644D875C9C955D4F32">
                    <OnDelete Action="Cascade"/>
                </End>
                <End Type="cds_api_equipment.EquipmentClassType" Multiplicity="*" Role="ToRole_assoc_0465B5D110F99F644D875C9C955D4F32"/>
            </Association>
            <Association Name="assoc_CFFEFD349364B3968B02807B151D07F0" sap:content-version="1">
                <End Type="cds_api_equipment.EquipmentClassCharacteristicType" Multiplicity="1" Role="FromRole_assoc_CFFEFD349364B3968B02807B151D07F0"/>
                <End Type="cds_api_equipment.EquipmentType" Multiplicity="1" Role="ToRole_assoc_CFFEFD349364B3968B02807B151D07F0"/>
            </Association>
            <Association Name="assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF" sap:content-version="1">
                <End Type="cds_api_equipment.EquipmentClassCharacteristicType" Multiplicity="1" Role="FromRole_assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF">
                    <OnDelete Action="Cascade"/>
                </End>
                <End Type="cds_api_equipment.EquipClassCharacteristicValueType" Multiplicity="*" Role="ToRole_assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF"/>
                <ReferentialConstraint>
                    <Principal Role="FromRole_assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF">
                        <PropertyRef Name="ClassInternalID"/>
                        <PropertyRef Name="CharacteristicInternalID"/>
                        <PropertyRef Name="Equipment"/>
                    </Principal>
                    <Dependent Role="ToRole_assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF">
                        <PropertyRef Name="ClassInternalID"/>
                        <PropertyRef Name="CharacteristicInternalID"/>
                        <PropertyRef Name="Equipment"/>
                    </Dependent>
                </ReferentialConstraint>
            </Association>
            <Association Name="assoc_BE0918A080660F98CF0DDB56BDB8CFE3" sap:content-version="1">
                <End Type="cds_api_equipment.EquipClassCharacteristicValueType" Multiplicity="1" Role="FromRole_assoc_BE0918A080660F98CF0DDB56BDB8CFE3"/>
                <End Type="cds_api_equipment.EquipmentType" Multiplicity="1" Role="ToRole_assoc_BE0918A080660F98CF0DDB56BDB8CFE3"/>
            </Association>
            <Association Name="assoc_68DE9F74E96A8F271658D66C1CD7D9A3" sap:content-version="1">
                <End Type="cds_api_equipment.EquipmentClassType" Multiplicity="1" Role="FromRole_assoc_68DE9F74E96A8F271658D66C1CD7D9A3">
                    <OnDelete Action="Cascade"/>
                </End>
                <End Type="cds_api_equipment.EquipmentClassCharacteristicType" Multiplicity="*" Role="ToRole_assoc_68DE9F74E96A8F271658D66C1CD7D9A3"/>
                <ReferentialConstraint>
                    <Principal Role="FromRole_assoc_68DE9F74E96A8F271658D66C1CD7D9A3">
                        <PropertyRef Name="ClassType"/>
                        <PropertyRef Name="ClassInternalID"/>
                        <PropertyRef Name="Equipment"/>
                    </Principal>
                    <Dependent Role="ToRole_assoc_68DE9F74E96A8F271658D66C1CD7D9A3">
                        <PropertyRef Name="ClassType"/>
                        <PropertyRef Name="ClassInternalID"/>
                        <PropertyRef Name="Equipment"/>
                    </Dependent>
                </ReferentialConstraint>
            </Association>
            <EntityContainer Name="cds_api_equipment_Entities" m:IsDefaultEntityContainer="true" sap:message-scope-supported="true" sap:supported-formats="atom json xlsx">
                <EntitySet Name="Equipment" EntityType="cds_api_equipment.EquipmentType" sap:deletable="false" sap:content-version="1"/>
                <EntitySet Name="EquipClassCharacteristicValue" EntityType="cds_api_equipment.EquipClassCharacteristicValueType" sap:deletable="false" sap:content-version="1"/>
                <EntitySet Name="EquipmentClass" EntityType="cds_api_equipment.EquipmentClassType" sap:content-version="1"/>
                <EntitySet Name="EquipmentClassCharacteristic" EntityType="cds_api_equipment.EquipmentClassCharacteristicType" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"/>
                <EntitySet Name="EquipmentLongText" EntityType="cds_api_equipment.EquipmentLongTextType" sap:deletable="false" sap:content-version="1"/>
                <EntitySet Name="EquipmentPartner" EntityType="cds_api_equipment.EquipmentPartnerType" sap:deletable="false" sap:content-version="1"/>
                <EntitySet Name="EquipmentWarranty" EntityType="cds_api_equipment.EquipmentWarrantyType" sap:deletable="false" sap:content-version="1"/>
                <AssociationSet Name="assoc_68DE9F74E96A8F271658D66C1CD7D9A3" Association="cds_api_equipment.assoc_68DE9F74E96A8F271658D66C1CD7D9A3" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1">
                    <End EntitySet="EquipmentClass" Role="FromRole_assoc_68DE9F74E96A8F271658D66C1CD7D9A3"/>
                    <End EntitySet="EquipmentClassCharacteristic" Role="ToRole_assoc_68DE9F74E96A8F271658D66C1CD7D9A3"/>
                </AssociationSet>
                <AssociationSet Name="assoc_0465B5D110F99F644D875C9C955D4F32" Association="cds_api_equipment.assoc_0465B5D110F99F644D875C9C955D4F32" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1">
                    <End EntitySet="Equipment" Role="FromRole_assoc_0465B5D110F99F644D875C9C955D4F32"/>
                    <End EntitySet="EquipmentClass" Role="ToRole_assoc_0465B5D110F99F644D875C9C955D4F32"/>
                </AssociationSet>
                <AssociationSet Name="assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF" Association="cds_api_equipment.assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1">
                    <End EntitySet="EquipmentClassCharacteristic" Role="FromRole_assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF"/>
                    <End EntitySet="EquipClassCharacteristicValue" Role="ToRole_assoc_8CDDDE39F452DC2B75EDBFBC9AED39CF"/>
                </AssociationSet>
                <AssociationSet Name="assoc_CFFEFD349364B3968B02807B151D07F0" Association="cds_api_equipment.assoc_CFFEFD349364B3968B02807B151D07F0" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1">
                    <End EntitySet="EquipmentClassCharacteristic" Role="FromRole_assoc_CFFEFD349364B3968B02807B151D07F0"/>
                    <End EntitySet="Equipment" Role="ToRole_assoc_CFFEFD349364B3968B02807B151D07F0"/>
                </AssociationSet>
                <AssociationSet Name="assoc_9565F4FC391DC57A0EC7360AC03FA518" Association="cds_api_equipment.assoc_9565F4FC391DC57A0EC7360AC03FA518" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1">
                    <End EntitySet="Equipment" Role="FromRole_assoc_9565F4FC391DC57A0EC7360AC03FA518"/>
                    <End EntitySet="EquipmentWarranty" Role="ToRole_assoc_9565F4FC391DC57A0EC7360AC03FA518"/>
                </AssociationSet>
                <AssociationSet Name="assoc_BE0918A080660F98CF0DDB56BDB8CFE3" Association="cds_api_equipment.assoc_BE0918A080660F98CF0DDB56BDB8CFE3" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1">
                    <End EntitySet="EquipClassCharacteristicValue" Role="FromRole_assoc_BE0918A080660F98CF0DDB56BDB8CFE3"/>
                    <End EntitySet="Equipment" Role="ToRole_assoc_BE0918A080660F98CF0DDB56BDB8CFE3"/>
                </AssociationSet>
                <AssociationSet Name="assoc_DA857AA25A191131134DCDEA3428DC9F" Association="cds_api_equipment.assoc_DA857AA25A191131134DCDEA3428DC9F" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1">
                    <End EntitySet="Equipment" Role="FromRole_assoc_DA857AA25A191131134DCDEA3428DC9F"/>
                    <End EntitySet="EquipmentLongText" Role="ToRole_assoc_DA857AA25A191131134DCDEA3428DC9F"/>
                </AssociationSet>
                <AssociationSet Name="assoc_8C45CFE9D5910E9A0B09F1A6F54222DD" Association="cds_api_equipment.assoc_8C45CFE9D5910E9A0B09F1A6F54222DD" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1">
                    <End EntitySet="Equipment" Role="FromRole_assoc_8C45CFE9D5910E9A0B09F1A6F54222DD"/>
                    <End EntitySet="EquipmentPartner" Role="ToRole_assoc_8C45CFE9D5910E9A0B09F1A6F54222DD"/>
                </AssociationSet>
                <FunctionImport Name="DismantleEquipment" ReturnType="cds_api_equipment.EquipmentType" EntitySet="Equipment" m:HttpMethod="POST" sap:action-for="cds_api_equipment.EquipmentType">
                    <Parameter Name="Equipment" Type="Edm.String" Mode="In" MaxLength="18" sap:label="Equipment"/>
                    <Parameter Name="ValidityEndDate" Type="Edm.DateTime" Mode="In" Precision="0" sap:label="Valid To" sap:display-format="Date"/>
                    <Parameter Name="SuperordinateEquipment" Type="Edm.String" Mode="In" MaxLength="18" Nullable="true" sap:label="Superord. Equipment"/>
                    <Parameter Name="EquipInstallationPositionNmbr" Type="Edm.String" Mode="In" MaxLength="6" Nullable="true" sap:label="Item (SD)"/>
                    <Parameter Name="FunctionalLocation" Type="Edm.String" Mode="In" MaxLength="40" Nullable="true" sap:label="Functional Location"/>
                    <Parameter Name="EquipmentInstallationDate" Type="Edm.DateTime" Mode="In" Precision="0" Nullable="true" sap:label="Changed On" sap:display-format="Date"/>
                    <Parameter Name="EquipmentInstallationTime" Type="Edm.Time" Mode="In" Precision="0" Nullable="true" sap:label="Time"/>
                </FunctionImport>
                <FunctionImport Name="DismantleEquipWithDataTransfer" ReturnType="cds_api_equipment.EquipmentType" EntitySet="Equipment" m:HttpMethod="POST" sap:action-for="cds_api_equipment.EquipmentType">
                    <Parameter Name="Equipment" Type="Edm.String" Mode="In" MaxLength="18" sap:label="Equipment"/>
                    <Parameter Name="ValidityEndDate" Type="Edm.DateTime" Mode="In" Precision="0" sap:label="Valid To" sap:display-format="Date"/>
                    <Parameter Name="SuperordinateEquipment" Type="Edm.String" Mode="In" MaxLength="18" Nullable="true" sap:label="Superord. Equipment"/>
                    <Parameter Name="FunctionalLocation" Type="Edm.String" Mode="In" MaxLength="40" Nullable="true" sap:label="Functional Location"/>
                    <Parameter Name="EquipInstallationPositionNmbr" Type="Edm.String" Mode="In" MaxLength="4" Nullable="true" sap:label="Position"/>
                    <Parameter Name="EquipmentInstallationDate" Type="Edm.DateTime" Mode="In" Precision="0" Nullable="true" sap:label="Changed On" sap:display-format="Date"/>
                    <Parameter Name="EquipmentInstallationTime" Type="Edm.Time" Mode="In" Precision="0" Nullable="true" sap:label="Time"/>
                    <Parameter Name="LocationCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="RoomCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="PlantSectionCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="WorkCenterCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="ABCIndicatorCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="SortFieldCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="BusinessAreaCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="CostCenterCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="WBSElementCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="StandingOrderCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="SettlementOrderCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="MaintPlanningPlantCopyIsReqd" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="MaintWorkCenterCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="CatalogProfileCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="SalesOrganizationCopyIsReqd" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                </FunctionImport>
                <FunctionImport Name="InstallEquipment" ReturnType="cds_api_equipment.EquipmentType" EntitySet="Equipment" m:HttpMethod="POST" sap:action-for="cds_api_equipment.EquipmentType">
                    <Parameter Name="Equipment" Type="Edm.String" Mode="In" MaxLength="18" sap:label="Equipment"/>
                    <Parameter Name="ValidityEndDate" Type="Edm.DateTime" Mode="In" Precision="0" sap:label="Valid To" sap:display-format="Date"/>
                    <Parameter Name="SuperordinateEquipment" Type="Edm.String" Mode="In" MaxLength="18" Nullable="true" sap:label="Superord. Equipment"/>
                    <Parameter Name="EquipInstallationPositionNmbr" Type="Edm.String" Mode="In" MaxLength="6" Nullable="true" sap:label="Item (SD)"/>
                    <Parameter Name="FunctionalLocation" Type="Edm.String" Mode="In" MaxLength="40" Nullable="true" sap:label="Functional Location"/>
                    <Parameter Name="EquipmentInstallationDate" Type="Edm.DateTime" Mode="In" Precision="0" Nullable="true" sap:label="Changed On" sap:display-format="Date"/>
                    <Parameter Name="EquipmentInstallationTime" Type="Edm.Time" Mode="In" Precision="0" Nullable="true" sap:label="Time"/>
                </FunctionImport>
                <FunctionImport Name="InstallEquipWithDataTransfer" ReturnType="cds_api_equipment.EquipmentType" EntitySet="Equipment" m:HttpMethod="POST" sap:action-for="cds_api_equipment.EquipmentType">
                    <Parameter Name="Equipment" Type="Edm.String" Mode="In" MaxLength="18" sap:label="Equipment"/>
                    <Parameter Name="ValidityEndDate" Type="Edm.DateTime" Mode="In" Precision="0" sap:label="Valid To" sap:display-format="Date"/>
                    <Parameter Name="SuperordinateEquipment" Type="Edm.String" Mode="In" MaxLength="18" Nullable="true" sap:label="Superord. Equipment"/>
                    <Parameter Name="FunctionalLocation" Type="Edm.String" Mode="In" MaxLength="40" Nullable="true" sap:label="Functional Location"/>
                    <Parameter Name="EquipInstallationPositionNmbr" Type="Edm.String" Mode="In" MaxLength="4" Nullable="true" sap:label="Position"/>
                    <Parameter Name="EquipmentInstallationDate" Type="Edm.DateTime" Mode="In" Precision="0" Nullable="true" sap:label="Changed On" sap:display-format="Date"/>
                    <Parameter Name="EquipmentInstallationTime" Type="Edm.Time" Mode="In" Precision="0" Nullable="true" sap:label="Time"/>
                    <Parameter Name="LocationCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="RoomCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="PlantSectionCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="WorkCenterCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="ABCIndicatorCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="SortFieldCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="BusinessAreaCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="CostCenterCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="WBSElementCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="StandingOrderCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="SettlementOrderCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="MaintPlanningPlantCopyIsReqd" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="MaintWorkCenterCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="CatalogProfileCopyIsRequested" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                    <Parameter Name="SalesOrganizationCopyIsReqd" Type="Edm.Boolean" Mode="In" Nullable="true" sap:label="Indicator"/>
                </FunctionImport>
                <FunctionImport Name="ResetEquipmentFromInactive" ReturnType="cds_api_equipment.DummyFunctionImportResult" m:HttpMethod="POST" sap:action-for="cds_api_equipment.EquipmentType">
                    <Parameter Name="Equipment" Type="Edm.String" Mode="In" MaxLength="18" sap:label="Equipment"/>
                    <Parameter Name="ValidityEndDate" Type="Edm.DateTime" Mode="In" Precision="0" sap:label="Valid To" sap:display-format="Date"/>
                </FunctionImport>
                <FunctionImport Name="RsetEquipFromMarkedForDeletion" ReturnType="cds_api_equipment.DummyFunctionImportResult" m:HttpMethod="POST" sap:action-for="cds_api_equipment.EquipmentType">
                    <Parameter Name="Equipment" Type="Edm.String" Mode="In" MaxLength="18" sap:label="Equipment"/>
                    <Parameter Name="ValidityEndDate" Type="Edm.DateTime" Mode="In" Precision="0" sap:label="Valid To" sap:display-format="Date"/>
                </FunctionImport>
                <FunctionImport Name="SetEquipmentToInactive" ReturnType="cds_api_equipment.DummyFunctionImportResult" m:HttpMethod="POST" sap:action-for="cds_api_equipment.EquipmentType">
                    <Parameter Name="Equipment" Type="Edm.String" Mode="In" MaxLength="18" sap:label="Equipment"/>
                    <Parameter Name="ValidityEndDate" Type="Edm.DateTime" Mode="In" Precision="0" sap:label="Valid To" sap:display-format="Date"/>
                </FunctionImport>
                <FunctionImport Name="SetEquipToMarkedForDeletion" ReturnType="cds_api_equipment.DummyFunctionImportResult" m:HttpMethod="POST" sap:action-for="cds_api_equipment.EquipmentType">
                    <Parameter Name="Equipment" Type="Edm.String" Mode="In" MaxLength="18" sap:label="Equipment"/>
                    <Parameter Name="ValidityEndDate" Type="Edm.DateTime" Mode="In" Precision="0" sap:label="Valid To" sap:display-format="Date"/>
                </FunctionImport>
            </EntityContainer>
            <Annotation Term="Core.SchemaVersion" String="1.1.0" xmlns="http://docs.oasis-open.org/odata/ns/edm"/>
            <Annotations Target="cds_api_equipment.cds_api_equipment_Entities" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.ApplyMultiUnitBehaviorForSortingAndFiltering" Bool="true"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentClassCharacteristicType/to_Value" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.Composition"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentClassType/to_Characteristic" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.Composition"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentType/to_Classification" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.Composition"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentType/to_LongText" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.Composition"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentType/to_Partner" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.Composition"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentType/to_Warranty" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.Composition"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipClassCharacteristicValueType/Characteristic" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.FieldControl" EnumMember="Common.FieldControlType/Mandatory"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipClassCharacteristicValueType/CharcDataType" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.FieldControl" EnumMember="Common.FieldControlType/Mandatory"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentClassCharacteristicType/Characteristic" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.FieldControl" EnumMember="Common.FieldControlType/Mandatory"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentLongTextType/EquipmentLongText" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.FieldControl" EnumMember="Common.FieldControlType/Mandatory"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentPartnerType/Partner" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.FieldControl" EnumMember="Common.FieldControlType/Mandatory"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentType/EquipmentCategory" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Common.FieldControl" EnumMember="Common.FieldControlType/Mandatory"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.cds_api_equipment_Entities/Equipment" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Capabilities.FilterRestrictions">
                    <Record>
                        <PropertyValue Property="NonFilterableProperties">
                            <Collection/>
                        </PropertyValue>
                    </Record>
                </Annotation>
                <Annotation Term="Capabilities.NavigationRestrictions">
                    <Record>
                        <PropertyValue Property="RestrictedProperties">
                            <Collection>
                                <Record>
                                    <PropertyValue Property="NavigationProperty" NavigationPropertyPath="to_Classification"/>
                                    <PropertyValue Property="InsertRestrictions">
                                        <Record>
                                            <PropertyValue Property="Insertable" Bool="true"/>
                                        </Record>
                                    </PropertyValue>
                                </Record>
                                <Record>
                                    <PropertyValue Property="NavigationProperty" NavigationPropertyPath="to_LongText"/>
                                    <PropertyValue Property="InsertRestrictions">
                                        <Record>
                                            <PropertyValue Property="Insertable" Bool="true"/>
                                        </Record>
                                    </PropertyValue>
                                </Record>
                                <Record>
                                    <PropertyValue Property="NavigationProperty" NavigationPropertyPath="to_Partner"/>
                                    <PropertyValue Property="InsertRestrictions">
                                        <Record>
                                            <PropertyValue Property="Insertable" Bool="true"/>
                                        </Record>
                                    </PropertyValue>
                                </Record>
                                <Record>
                                    <PropertyValue Property="NavigationProperty" NavigationPropertyPath="to_Warranty"/>
                                    <PropertyValue Property="InsertRestrictions">
                                        <Record>
                                            <PropertyValue Property="Insertable" Bool="true"/>
                                        </Record>
                                    </PropertyValue>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>
                <Annotation Term="SAP__core.OptimisticConcurrency">
                    <Collection>
                        <PropertyPath>LastChangeDateTime</PropertyPath>
                    </Collection>
                </Annotation>
                <Annotation Term="Capabilities.SortRestrictions">
                    <Record>
                        <PropertyValue Property="NonSortableProperties">
                            <Collection/>
                        </PropertyValue>
                    </Record>
                </Annotation>
            </Annotations>
            <Annotations Target="cds_api_equipment.cds_api_equipment_Entities/EquipmentLongText" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Capabilities.FilterRestrictions">
                    <Record>
                        <PropertyValue Property="FilterExpressionRestrictions">
                            <Collection>
                                <Record>
                                    <PropertyValue Property="Property" PropertyPath="EquipmentLongText"/>
                                    <PropertyValue Property="AllowedExpressions" String="SearchExpression"/>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>
                <Annotation Term="SAP__core.OptimisticConcurrency">
                    <Collection/>
                </Annotation>
                <Annotation Term="Capabilities.SortRestrictions">
                    <Record>
                        <PropertyValue Property="NonSortableProperties">
                            <Collection>
                                <PropertyPath>EquipmentLongText</PropertyPath>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipClassCharacteristicValueType/CharcValuePositionNumber" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipClassCharacteristicValueType/ClassInternalID" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipClassCharacteristicValueType/ClassType" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipClassCharacteristicValueType/Equipment" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentClassCharacteristicType/CharacteristicInternalID" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentClassCharacteristicType/ClassInternalID" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentClassCharacteristicType/ClassType" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentClassCharacteristicType/Equipment" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentClassType/ClassInternalID" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentClassType/ClassType" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentClassType/Equipment" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentLongTextType/Equipment" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentPartnerType/Equipment" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentPartnerType/EquipmentPartnerObjectNmbr" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentPartnerType/PartnerFunction" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentType/Equipment" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentWarrantyType/Equipment" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.EquipmentWarrantyType/WarrantyType" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.Immutable"/>
            </Annotations>
            <Annotations Target="cds_api_equipment.cds_api_equipment_Entities/EquipClassCharacteristicValue" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Capabilities.InsertRestrictions">
                    <Record>
                        <PropertyValue Property="RequiredProperties">
                            <Collection>
                                <PropertyPath>ClassInternalID</PropertyPath>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>
                <Annotation Term="SAP__core.OptimisticConcurrency">
                    <Collection/>
                </Annotation>
            </Annotations>
            <Annotations Target="cds_api_equipment.cds_api_equipment_Entities/EquipmentClassCharacteristic" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Capabilities.InsertRestrictions">
                    <Record>
                        <PropertyValue Property="RequiredProperties">
                            <Collection>
                                <PropertyPath>ClassInternalID</PropertyPath>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>
                <Annotation Term="Capabilities.NavigationRestrictions">
                    <Record>
                        <PropertyValue Property="RestrictedProperties">
                            <Collection>
                                <Record>
                                    <PropertyValue Property="NavigationProperty" NavigationPropertyPath="to_Value"/>
                                    <PropertyValue Property="InsertRestrictions">
                                        <Record>
                                            <PropertyValue Property="Insertable" Bool="true"/>
                                        </Record>
                                    </PropertyValue>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>
                <Annotation Term="SAP__core.OptimisticConcurrency">
                    <Collection/>
                </Annotation>
            </Annotations>
            <Annotations Target="cds_api_equipment.cds_api_equipment_Entities/EquipmentClass" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Capabilities.NavigationRestrictions">
                    <Record>
                        <PropertyValue Property="RestrictedProperties">
                            <Collection>
                                <Record>
                                    <PropertyValue Property="NavigationProperty" NavigationPropertyPath="to_Characteristic"/>
                                    <PropertyValue Property="InsertRestrictions">
                                        <Record>
                                            <PropertyValue Property="Insertable" Bool="true"/>
                                        </Record>
                                    </PropertyValue>
                                </Record>
                            </Collection>
                        </PropertyValue>
                    </Record>
                </Annotation>
                <Annotation Term="SAP__core.OptimisticConcurrency">
                    <Collection/>
                </Annotation>
            </Annotations>
            <Annotations Target="cds_api_equipment.cds_api_equipment_Entities/EquipmentPartner" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.OptimisticConcurrency">
                    <Collection/>
                </Annotation>
            </Annotations>
            <Annotations Target="cds_api_equipment.cds_api_equipment_Entities/EquipmentWarranty" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="SAP__core.OptimisticConcurrency">
                    <Collection/>
                </Annotation>
            </Annotations>
            <Annotations Target="cds_api_equipment.cds_api_equipment_Entities" xmlns="http://docs.oasis-open.org/odata/ns/edm">
                <Annotation Term="Org.OData.Capabilities.V1.BatchSupport">
                    <Record Type="Org.OData.Capabilities.V1.BatchSupportType">
                        <PropertyValue Property="ReferencesAcrossChangeSetsSupported" Bool="true"/>
                    </Record>
                </Annotation>
            </Annotations>
            <atom:link rel="self" href="http://s4ha.unvired.io:8001/sap/opu/odata/sap/API_EQUIPMENT/$metadata" xmlns:atom="http://www.w3.org/2005/Atom"/>
            <atom:link rel="latest-version" href="http://s4ha.unvired.io:8001/sap/opu/odata/sap/API_EQUIPMENT/$metadata" xmlns:atom="http://www.w3.org/2005/Atom"/>
        </Schema>
    </edmx:DataServices>
</edmx:Edmx>