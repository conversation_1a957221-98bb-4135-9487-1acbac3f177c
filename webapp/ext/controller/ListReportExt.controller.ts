import Controller from "sap/ui/core/mvc/Controller";
import Event from "sap/ui/base/Event";

/**
 * @namespace com.unvired.demo.equipment.equipsafetytestapp.ext.controller
 */
export default class ListReportExt extends Controller {

    /**
     * Called when the controller is instantiated.
     * @public
     */
    public onInit(): void {
        // Controller extension initialization
        console.log("ListReportExt controller initialized");
    }

    /**
     * Event handler for when the smart table is initialized
     * This ensures all LineItem fields are selected in the OData request
     * @param oEvent - The event object
     * @public
     */
    public onBeforeRebindTable(oEvent: Event): void {
        console.log("onBeforeRebindTable called");
        const oBindingParams = oEvent.getParameter("bindingParams");
        const oSmartTable = oEvent.getSource();

        if (oBindingParams && oSmartTable) {
            console.log("Original binding params:", oBindingParams);

            // Get all fields from the LineItem annotation
            const aLineItemFields = [
                "Equipment",
                "EquipmentName",
                "EquipmentCategory",
                "TechnicalObjectType",
                "MaintenancePlant",
                "AssetLocation",
                "WorkCenter",
                "FunctionalLocationLabelName",
                "AssetManufacturerName",
                "ManufacturerPartTypeName",
                "ManufacturerSerialNumber",
                "OperationStartDate",
                "ValidityStartDate",
                "ValidityEndDate",
                "CreationDate",
                "CreatedByUser"
            ];

            // Ensure all LineItem fields are included in the select parameter
            if (!oBindingParams.parameters) {
                oBindingParams.parameters = {};
            }

            if (!oBindingParams.parameters.select) {
                oBindingParams.parameters.select = aLineItemFields.join(",");
            } else {
                // Merge with existing select fields
                const aExistingFields = oBindingParams.parameters.select.split(",");
                const aAllFields = [...new Set([...aExistingFields, ...aLineItemFields])];
                oBindingParams.parameters.select = aAllFields.join(",");
            }

            console.log("Modified binding params:", oBindingParams);
        }
    }

    /**
     * Extension point for List Report
     * @public
     */
    public onListNavigationExtension(): any {
        return {
            "onBeforeRebindTable": this.onBeforeRebindTable.bind(this)
        };
    }
}
