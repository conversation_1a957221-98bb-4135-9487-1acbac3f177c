import Controller from "sap/ui/core/mvc/Controller";
import Event from "sap/ui/base/Event";

/**
 * @namespace com.unvired.demo.equipment.equipsafetytestapp.ext.controller
 */
export default class ListReportExt extends Controller {

    /**
     * Called when the controller is instantiated.
     * @public
     */
    public onInit(): void {
        // Controller extension initialization
        console.log("ListReportExt controller initialized");
    }

    /**
     * Event handler for when the smart table is initialized
     * This ensures all LineItem fields are selected in the OData request
     * @param oEvent - The event object
     * @public
     */
    public onBeforeRebindTable(oEvent: Event): void {
        console.log("onBeforeRebindTable called");
        const oBindingParams = oEvent.getParameter("bindingParams");

        if (oBindingParams) {
            console.log("Original binding params:", oBindingParams);

            // Get all fields from the LineItem annotation - these are the fields we want to display
            const sAllFields = "Equipment,EquipmentName,EquipmentCategory,TechnicalObjectType,MaintenancePlant,AssetLocation,WorkCenter,FunctionalLocationLabelName,AssetManufacturerName,ManufacturerPartTypeName,ManufacturerSerialNumber,OperationStartDate,ValidityStartDate,ValidityEndDate,CreationDate,CreatedByUser";

            // Force the select parameter to include all our fields
            if (!oBindingParams.parameters) {
                oBindingParams.parameters = {};
            }

            // Always override the select parameter with all fields
            oBindingParams.parameters.select = sAllFields;

            console.log("Modified binding params with select:", oBindingParams.parameters.select);
        }
    }

    /**
     * Extension point for List Report
     * @public
     */
    public onListNavigationExtension(): any {
        return {
            "onBeforeRebindTable": this.onBeforeRebindTable.bind(this)
        };
    }
}
