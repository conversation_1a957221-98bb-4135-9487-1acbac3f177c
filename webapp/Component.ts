import BaseComponent from "sap/suite/ui/generic/template/lib/AppComponent";

/**
 * @namespace com.unvired.demo.equipment.equipsafetytestapp
 */
export default class Component extends BaseComponent {

	public static metadata = {
		manifest: "json"
	};

    /**
     * The component is initialized by UI5 automatically during the startup of the app and calls the init method once.
     * @public
     * @override
     */
	public init() : void {
        super.init();

        // Override the default binding behavior to include all LineItem fields
        this._overrideSmartTableBinding();
	}

    /**
     * Override Smart Table binding to include all fields from LineItem annotation
     * @private
     */
    private _overrideSmartTableBinding(): void {
        const oModel = this.getModel();
        if (oModel) {
            // Set default request fields for Equipment entity
            const sAllFields = "Equipment,EquipmentName,EquipmentCategory,TechnicalObjectType,MaintenancePlant,AssetLocation,WorkCenter,FunctionalLocationLabelName,AssetManufacturerName,ManufacturerPartTypeName,ManufacturerSerialNumber,OperationStartDate,ValidityStartDate,ValidityEndDate,CreationDate,CreatedByUser";

            // Override the default binding parameters
            const fnOriginalBindList = oModel.bindList;
            oModel.bindList = function(sPath: string, oContext?: any, aSorters?: any, aFilters?: any, mParameters?: any) {
                if (sPath === "/Equipment" && mParameters) {
                    if (!mParameters.select) {
                        mParameters.select = sAllFields;
                    }
                }
                return fnOriginalBindList.call(this, sPath, oContext, aSorters, aFilters, mParameters);
            };
        }
    }
}