{"_version": "1.53.0", "sap.app": {"id": "com.unvired.demo.equipment.equipsafetytestapp", "type": "application", "i18n": "i18n/i18n.properties", "applicationVersion": {"version": "0.0.1"}, "title": "{{appTitle}}", "description": "{{appDescription}}", "resources": "resources.json", "sourceTemplate": {"id": "@sap/generator-fiori:lrop", "version": "1.18.7", "toolsId": "4df66176-5003-4924-9c34-f1cf3c5ef6ad"}, "dataSources": {"annotation": {"type": "ODataAnnotation", "uri": "annotations/annotation.xml", "settings": {"localUri": "annotations/annotation.xml"}}, "mainService": {"uri": "/sap/opu/odata/sap/API_EQUIPMENT/", "type": "OData", "settings": {"annotations": ["annotation"], "localUri": "localService/mainService/metadata.xml", "odataVersion": "2.0"}}}}, "sap.ui": {"technology": "UI5", "icons": {"icon": "", "favIcon": "", "phone": "", "phone@2": "", "tablet": "", "tablet@2": ""}, "deviceTypes": {"desktop": true, "tablet": true, "phone": true}}, "sap.ui5": {"flexEnabled": true, "dependencies": {"minUI5Version": "1.114.0", "libs": {"sap.m": {}, "sap.ui.core": {}, "sap.ushell": {}, "sap.f": {}, "sap.ui.comp": {}, "sap.ui.generic.app": {}, "sap.suite.ui.generic.template": {}}}, "contentDensities": {"compact": true, "cozy": true}, "models": {"i18n": {"type": "sap.ui.model.resource.ResourceModel", "settings": {"bundleName": "com.unvired.demo.equipment.equipsafetytestapp.i18n.i18n"}}, "": {"dataSource": "mainService", "preload": true, "settings": {"defaultBindingMode": "TwoWay", "defaultCountMode": "Inline", "refreshAfterChange": false, "metadataUrlParams": {"sap-value-list": "none"}, "preliminaryContext": true, "skipMetadataAnnotationParsing": false, "defaultOperationMode": "Server", "defaultUpdateMethod": "PUT"}}, "@i18n": {"type": "sap.ui.model.resource.ResourceModel", "uri": "i18n/i18n.properties"}}, "resources": {"css": []}, "routing": {"config": {}, "routes": [], "targets": {}}}, "sap.ui.generic.app": {"_version": "1.3.0", "settings": {"forceGlobalRefresh": false, "objectPageHeaderType": "Dynamic", "considerAnalyticalParameters": true, "showDraftToggle": false}, "pages": {"ListReport|Equipment": {"entitySet": "Equipment", "component": {"name": "sap.suite.ui.generic.template.ListReport", "list": true, "settings": {"condensedTableLayout": true, "smartVariantManagement": true, "enableTableFilterInPageVariant": true, "filterSettings": {"dateSettings": {"useDateRange": true}}, "tableSettings": {"type": "ResponsiveTable", "selectAll": true, "enableAutoColumnWidth": true, "multiSelect": false, "enableExport": true, "personalization": {"column": true, "sort": true, "filter": true}, "requestAtLeastFields": "Equipment,EquipmentName,EquipmentCategory,TechnicalObjectType,MaintenancePlant,AssetLocation,WorkCenter,FunctionalLocationLabelName,AssetManufacturerName,ManufacturerPartTypeName,ManufacturerSerialNumber,OperationStartDate,ValidityStartDate,ValidityEndDate,CreationDate,CreatedByUser"}, "dataLoadSettings": {"loadDataOnAppLaunch": "always"}, "controlConfiguration": {"@com.sap.vocabularies.UI.v1.LineItem": {"tableSettings": {"selectAll": true, "requestAtLeastFields": "Equipment,EquipmentName,EquipmentCategory,TechnicalObjectType,MaintenancePlant,AssetLocation,WorkCenter,FunctionalLocationLabelName,AssetManufacturerName,ManufacturerPartTypeName,ManufacturerSerialNumber,OperationStartDate,ValidityStartDate,ValidityEndDate,CreationDate,CreatedByUser"}}}, "controllerExtensions": {"sap.suite.ui.generic.template.ListReport.view.ListReport": {"controllerName": "com.unvired.demo.equipment.equipsafetytestapp.ext.controller.ListReportExt"}}}}, "pages": {"ObjectPage|Equipment": {"entitySet": "Equipment", "defaultLayoutTypeIfExternalNavigation": "MidColumnFullScreen", "component": {"name": "sap.suite.ui.generic.template.ObjectPage"}, "pages": {"ObjectPage|to_Classification": {"navigationProperty": "to_Classification", "entitySet": "EquipmentClass", "defaultLayoutTypeIfExternalNavigation": "MidColumnFullScreen", "component": {"name": "sap.suite.ui.generic.template.ObjectPage"}}}}}}}}, "sap.fiori": {"registrationIds": [], "archeType": "transactional"}}