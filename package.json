{"name": "equip-safety-test-app", "version": "0.0.1", "description": "An SAP Fiori Equipment Safety Testing App", "keywords": ["ui5", "openui5", "sapui5"], "main": "webapp/index.html", "dependencies": {}, "devDependencies": {"@ui5/cli": "^4.0.16", "@sap/ux-ui5-tooling": "1", "@sapui5/types": "~1.114.0", "ui5-tooling-transpile": "^3.3.7", "typescript": "^5.1.6", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@sap-ux/eslint-plugin-fiori-tools": "^0.4.0", "@sap-ux/ui5-middleware-fe-mockserver": "2"}, "scripts": {"start": "fiori run --open \"test/flp.html#app-preview\"", "start-local": "fiori run --config ./ui5-local.yaml --open \"test/flp.html#app-preview\"", "build": "ui5 build --config=ui5.yaml --clean-dest --dest dist", "lint": "eslint ./", "ts-typecheck": "tsc --noEmit", "prestart": "npm run ts-typecheck", "prebuild": "npm run ts-typecheck", "start-mock": "fiori run --config ./ui5-mock.yaml --open \"test/flp.html#app-preview\"", "deploy": "fiori verify", "deploy-config": "fiori add deploy-config", "start-noflp": "fiori run --open \"/index.html?sap-ui-xx-viewCache=false\"", "start-variants-management": "fiori run --open \"/preview.html#app-preview\""}, "sapuxLayer": "CUSTOMER_BASE", "sapux": true}