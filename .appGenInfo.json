{"generationParameters": {"generationDate": "Sun Sep 28 2025 18:54:17 GMT+0530 (India Standard Time)", "generatorPlatform": "Visual Studio Code", "serviceType": "SAP System (ABAP On-Premise)", "metadataFilename": "", "serviceUrl": "http://s4ha.unvired.io:8001/sap/opu/odata/sap/API_EQUIPMENT", "appName": "equip-safety-test-app", "appTitle": "Equipment Safety Testing", "appDescription": "An SAP Fiori Equipment Safety Testing App", "appNamespace": "com.unvired.demo.equipment", "ui5Theme": "sap_horizon", "ui5Version": "1.114.0", "enableCodeAssist": false, "enableEslint": false, "enableTypeScript": true, "showMockDataInfo": true, "generatorVersion": "1.18.7", "template": "List Report Page V2", "generatorName": "SAP Fiori Application Generator", "entityRelatedConfig": [{"type": "Main Entity", "value": "Equipment"}, {"type": "Navigation Entity", "value": "to_Classification"}], "launchText": "To launch the generated application, run the following from the generated application root folder:\n\n```\n    npm start\n```"}}